<launch>
<!-- 定位模式 Launch file for Livox MID-360 -->
<!-- 使用已保存的地图进行定位，不更新地图 -->

	<arg name="rviz" default="0" />

	<!-- 加载MID-360配置 -->
	<rosparam command="load" file="$(find faster_lio)/config/mid360.yaml" />

	<!-- 定位模式参数覆盖 -->
	<param name="pcd_save/pcd_save_en" type="bool" value="false" />        <!-- 不保存点云 -->
	<param name="path_save_en" type="bool" value="false" />                <!-- 不保存轨迹 -->
	<param name="runtime_pos_log_enable" type="bool" value="false" />      <!-- 不记录位置日志 -->

	<!-- 定位模式标志 -->
	<param name="localization_mode" type="bool" value="true" />            <!-- 启用定位模式 -->

	<!-- 发布设置 -->
	<param name="publish/path_publish_en" type="bool" value="true" />       <!-- 发布路径 -->
	<param name="publish/scan_publish_en" type="bool" value="true" />       <!-- 发布扫描 -->
	<param name="publish/scan_effect_pub_en" type="bool" value="true" />    <!-- 发布有效点 -->
	<param name="publish/dense_publish_en" type="bool" value="false" />     <!-- 不发布密集点云 -->
	<param name="publish/scan_bodyframe_pub_en" type="bool" value="true" /> <!-- 发布机体坐标系点云 -->

	<!-- Launch参数 -->
	<param name="feature_extract_enable" type="bool" value="false"/>
	<param name="point_filter_num_" type="int" value="1"/>
	<param name="max_iteration" type="int" value="3" />
	<param name="filter_size_surf" type="double" value="0.1" />
	<param name="filter_size_map" type="double" value="0.1" />
	<param name="cube_side_length" type="double" value="1000" />

    <!-- 启动faster_lio定位节点 -->
    <node pkg="faster_lio" type="run_mapping_online" name="laserMapping" output="screen"
          args="--traj_log_file=$(find faster_lio)/Log/traj.txt" />

    <!-- 注意：静态地图需要单独运行脚本加载 -->

    <!-- 启动rviz -->
    <group if="$(arg rviz)">
        <node launch-prefix="nice" pkg="rviz" type="rviz" name="rviz"
              args="-d $(find faster_lio)/rviz_cfg/loam_livox.rviz" />
    </group>

</launch>
