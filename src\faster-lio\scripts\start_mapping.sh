#!/bin/bash
# 建图启动脚本

echo "=== Faster-LIO 建图系统 ==="
echo "1. 开始建图（带rviz）"
echo "2. 开始建图（不带rviz）"
echo "3. 标记篮筐位置"
echo "4. 退出"
echo

read -p "请选择操作 (1-4): " choice

case $choice in
    1)
        echo "启动建图系统（带rviz）..."
        echo "建图完成后按Ctrl+C保存地图"
        roslaunch faster_lio mapping_mid360.launch rviz:=true
        ;;
    2)
        echo "启动建图系统（不带rviz）..."
        echo "建图完成后按Ctrl+C保存地图"
        roslaunch faster_lio mapping_mid360.launch
        ;;
    3)
        echo "启动篮筐标记器..."
        echo "请确保建图系统正在运行！"
        echo "按空格键标记篮筐位置，按q退出"
        python3 ~/faster_lio/src/faster-lio/scripts/mark_point.py
        ;;
    4)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac
