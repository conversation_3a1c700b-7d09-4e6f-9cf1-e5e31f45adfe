<launch>
<!-- Launch file for Livox MID-360 LiDAR -->

	<arg name="rviz" default="false" />

	<rosparam command="load" file="$(find faster_lio)/config/mid360.yaml" />

	<param name="feature_extract_enable" type="bool" value="0"/>
	<param name="point_filter_num_" type="int" value="1"/>
	<param name="max_iteration" type="int" value="3" />
	<param name="filter_size_surf" type="double" value="0.1" />
	<param name="filter_size_map" type="double" value="0.1" />
	<param name="cube_side_length" type="double" value="100" />
	<param name="runtime_pos_log_enable" type="bool" value="1" />
    <node pkg="faster_lio" type="run_mapping_online" name="laserMapping" output="screen"
          args="--traj_log_file=$(find faster_lio)/Log/traj.txt" />

    <group if="$(arg rviz)">
        <node launch-prefix="nice" pkg="rviz" type="rviz" name="rviz" args="-d $(find faster_lio)/rviz_cfg/loam_livox.rviz" />
    </group>

</launch>

