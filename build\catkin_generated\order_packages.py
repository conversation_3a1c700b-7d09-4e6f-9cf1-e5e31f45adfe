# generated from catkin/cmake/template/order_packages.context.py.in
source_root_dir = '/home/<USER>/faster_lio/src'
whitelisted_packages = ''.split(';') if '' != '' else []
blacklisted_packages = ''.split(';') if '' != '' else []
underlay_workspaces = '/home/<USER>/Reproduce-FAST-LIVO2/devel;/home/<USER>/r2_2/devel;/opt/ros/noetic'.split(';') if '/home/<USER>/Reproduce-FAST-LIVO2/devel;/home/<USER>/r2_2/devel;/opt/ros/noetic' != '' else []
