#!/bin/bash
# 导航启动脚本

echo "=== Faster-LIO 导航系统 ==="
echo "1. 启动定位系统（加载地图）"
echo "2. 启动篮筐定位（计算角度距离）"
echo "3. 独立重定位（推荐 - 不依赖建图模式）"
echo "4. 自动重定位（需要faster_lio运行）"
echo "5. 精确重定位（在标记点位置时使用）"
echo "6. 手动重定位"
echo "7. 退出"
echo

read -p "请选择操作 (1-7): " choice

case $choice in
    1)
        echo "启动定位系统..."
        echo "将加载保存的地图和篮筐标记点"
        echo "请在rviz中查看地图加载情况"
        python3 ~/faster_lio/src/faster-lio/scripts/load_map_with_points.py
        ;;
    2)
        echo "启动篮筐角度距离计算..."
        echo "此系统会："
        echo "- 订阅机器人位置数据"
        echo "- 实时计算与篮筐的角度和距离"
        echo "- 发布到 /y_angle, /xy_distance, /x_distance 话题"
        echo ""
        echo "请确保faster_lio定位系统正在运行！"
        echo "请确保已通过选项1加载了地图！"
        python3 ~/faster_lio/src/faster-lio/scripts/basket_calculator.py
        ;;
    3)
        echo "独立重定位..."
        echo "不依赖faster_lio建图模式，直接使用原始激光雷达数据"
        echo "适用于任意位置的重定位"
        echo "请确保MID-360正在运行！"
        python3 ~/faster_lio/src/faster-lio/scripts/standalone_relocalize.py
        ;;
    4)
        echo "自动重定位..."
        echo "通过点云匹配自动确定机器人位置"
        echo "需要faster_lio定位系统正在运行"
        python3 ~/faster_lio/src/faster-lio/scripts/auto_relocalize.py
        ;;
    5)
        echo "精确重定位..."
        echo "适用于机器人位于已知标记点位置的情况"
        echo "将机器人精确定位到标记点坐标"
        python3 ~/faster_lio/src/faster-lio/scripts/precise_relocalize.py
        ;;
    6)
        echo "手动重定位..."
        echo "用于在地图中手动设置机器人位置"
        python3 ~/faster_lio/src/faster-lio/scripts/relocalize.py
        ;;
    7)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac
