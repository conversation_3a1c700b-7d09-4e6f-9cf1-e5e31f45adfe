#!/usr/bin/env python3
"""
地图加载和标记点显示脚本
加载保存的点云地图和标记点，在rviz中显示
使用方法：
python3 scripts/load_map_with_points.py [点云文件路径] [标记点文件路径]
"""

import rospy
import json
import sys
import os
import numpy as np
from sensor_msgs.msg import PointCloud2, PointField
from visualization_msgs.msg import Marker, MarkerArray
from std_msgs.msg import Header
import sensor_msgs.point_cloud2 as pc2

try:
    import open3d as o3d
    HAS_OPEN3D = True
except ImportError:
    HAS_OPEN3D = False
    print("警告: 未安装open3d，将尝试使用PCL加载点云")

class MapLoader:
    def __init__(self):
        rospy.init_node('map_loader', anonymous=True)
        
        # ROS发布器
        self.cloud_pub = rospy.Publisher('/loaded_map', PointCloud2, queue_size=1, latch=True)
        self.marker_pub = rospy.Publisher('/loaded_marked_points', MarkerArray, queue_size=1, latch=True)
        
        print("=== 地图和标记点加载器 ===")
    
    def load_pointcloud(self, pcd_file):
        """加载点云文件"""
        if not os.path.exists(pcd_file):
            print(f"错误: 点云文件不存在: {pcd_file}")
            return None
        
        print(f"加载点云文件: {pcd_file}")
        
        if HAS_OPEN3D:
            return self.load_with_open3d(pcd_file)
        else:
            return self.load_with_pcl(pcd_file)
    
    def load_with_open3d(self, pcd_file):
        """使用Open3D加载点云"""
        try:
            pcd = o3d.io.read_point_cloud(pcd_file)
            points = np.asarray(pcd.points)
            
            if pcd.has_colors():
                colors = np.asarray(pcd.colors)
                return points, colors
            else:
                return points, None
                
        except Exception as e:
            print(f"Open3D加载失败: {e}")
            return None, None
    
    def load_with_pcl(self, pcd_file):
        """使用PCL Python加载点云（备用方案）"""
        try:
            # 这里可以添加PCL Python的加载代码
            # 或者直接读取ASCII格式的PCD文件
            print("尝试读取ASCII格式的PCD文件...")
            return self.load_ascii_pcd(pcd_file)
        except Exception as e:
            print(f"PCL加载失败: {e}")
            return None, None
    
    def load_ascii_pcd(self, pcd_file):
        """读取ASCII格式的PCD文件"""
        points = []
        with open(pcd_file, 'r') as f:
            lines = f.readlines()
            
            # 查找DATA行
            data_start = 0
            for i, line in enumerate(lines):
                if line.startswith('DATA'):
                    data_start = i + 1
                    break
            
            # 读取点数据
            for line in lines[data_start:]:
                if line.strip():
                    parts = line.strip().split()
                    if len(parts) >= 3:
                        try:
                            x, y, z = float(parts[0]), float(parts[1]), float(parts[2])
                            points.append([x, y, z])
                        except ValueError:
                            continue
        
        return np.array(points), None
    
    def points_to_pointcloud2(self, points, colors=None, frame_id="camera_init"):
        """将numpy点云转换为ROS PointCloud2消息"""
        header = Header()
        header.stamp = rospy.Time.now()
        header.frame_id = frame_id
        
        if colors is not None:
            # 带颜色的点云
            fields = [
                PointField('x', 0, PointField.FLOAT32, 1),
                PointField('y', 4, PointField.FLOAT32, 1),
                PointField('z', 8, PointField.FLOAT32, 1),
                PointField('rgb', 12, PointField.UINT32, 1),
            ]
            
            # 转换颜色格式
            rgb_colors = []
            for color in colors:
                r = int(color[0] * 255)
                g = int(color[1] * 255)
                b = int(color[2] * 255)
                rgb = (r << 16) | (g << 8) | b
                rgb_colors.append(rgb)
            
            # 组合点和颜色
            cloud_data = []
            for i, point in enumerate(points):
                cloud_data.append([point[0], point[1], point[2], rgb_colors[i]])
                
        else:
            # 只有坐标的点云
            fields = [
                PointField('x', 0, PointField.FLOAT32, 1),
                PointField('y', 4, PointField.FLOAT32, 1),
                PointField('z', 8, PointField.FLOAT32, 1),
            ]
            cloud_data = points.tolist()
        
        return pc2.create_cloud(header, fields, cloud_data)
    
    def load_marked_points(self, points_file):
        """加载标记点文件"""
        if not os.path.exists(points_file):
            print(f"错误: 标记点文件不存在: {points_file}")
            return None
        
        print(f"加载标记点文件: {points_file}")
        
        try:
            with open(points_file, 'r', encoding='utf-8') as f:
                points = json.load(f)
            return points
        except Exception as e:
            print(f"加载标记点失败: {e}")
            return None
    
    def publish_marked_points(self, marked_points):
        """发布标记点的可视化"""
        if not marked_points:
            return
        
        marker_array = MarkerArray()
        
        for i, point in enumerate(marked_points):
            # 创建球形标记
            marker = Marker()
            marker.header.frame_id = point.get('frame_id', 'camera_init')
            marker.header.stamp = rospy.Time.now()
            marker.ns = "loaded_marked_points"
            marker.id = i
            marker.type = Marker.SPHERE
            marker.action = Marker.ADD
            
            # 位置
            marker.pose.position.x = point['x']
            marker.pose.position.y = point['y']
            marker.pose.position.z = point['z'] + 1.63  # 雷达正上方1.63米
            marker.pose.orientation.w = 1.0
            
            # 大小和颜色
            marker.scale.x = 1.5
            marker.scale.y = 1.5
            marker.scale.z = 1.5
            marker.color.r = 0.0
            marker.color.g = 1.0
            marker.color.b = 0.0
            marker.color.a = 0.8
            
            marker_array.markers.append(marker)
            
            # 创建文本标记
            text_marker = Marker()
            text_marker.header = marker.header
            text_marker.ns = "loaded_marked_points_text"
            text_marker.id = i
            text_marker.type = Marker.TEXT_VIEW_FACING
            text_marker.action = Marker.ADD
            
            text_marker.pose.position.x = point['x']
            text_marker.pose.position.y = point['y']
            text_marker.pose.position.z = point['z'] + 2.63  # 雷达正上方2.63米（1.63+1.0文字偏移）
            text_marker.pose.orientation.w = 1.0
            
            text_marker.scale.z = 1.2
            text_marker.color.r = 1.0
            text_marker.color.g = 1.0
            text_marker.color.b = 0.0
            text_marker.color.a = 1.0
            
            text_marker.text = point.get('description', f"Point_{i}")
            
            marker_array.markers.append(text_marker)
        
        self.marker_pub.publish(marker_array)
        print(f"✓ 发布了 {len(marked_points)} 个标记点")
    
    def run(self, pcd_file=None, points_file=None):
        """运行地图加载器"""
        # 默认文件路径
        if pcd_file is None:
            pcd_file = os.path.expanduser("~/faster_lio/src/faster-lio/PCD/scans.pcd")
        if points_file is None:
            # 查找最新的标记点文件
            points_dir = os.path.expanduser("~/faster_lio/marked_points")
            if os.path.exists(points_dir):
                json_files = [f for f in os.listdir(points_dir) if f.endswith('.json')]
                if json_files:
                    json_files.sort(reverse=True)  # 按时间排序，最新的在前
                    points_file = os.path.join(points_dir, json_files[0])
                    print(f"自动选择最新的标记点文件: {points_file}")
        
        # 加载点云
        if pcd_file and os.path.exists(pcd_file):
            points, colors = self.load_pointcloud(pcd_file)
            if points is not None:
                cloud_msg = self.points_to_pointcloud2(points, colors)
                self.cloud_pub.publish(cloud_msg)
                print(f"✓ 发布了点云地图，包含 {len(points)} 个点")
            else:
                print("✗ 点云加载失败")
        else:
            print(f"警告: 点云文件不存在: {pcd_file}")
        
        # 加载标记点
        if points_file and os.path.exists(points_file):
            marked_points = self.load_marked_points(points_file)
            if marked_points:
                self.publish_marked_points(marked_points)
            else:
                print("✗ 标记点加载失败")
        else:
            print(f"警告: 标记点文件不存在: {points_file}")
        
        print("\n地图和标记点已加载完成")
        print("请在rviz中添加以下话题进行查看:")
        print("  - /loaded_map (PointCloud2)")
        print("  - /loaded_marked_points (MarkerArray)")
        
        # 保持节点运行
        rospy.spin()

if __name__ == '__main__':
    try:
        loader = MapLoader()
        
        # 解析命令行参数
        pcd_file = sys.argv[1] if len(sys.argv) > 1 else None
        points_file = sys.argv[2] if len(sys.argv) > 2 else None
        
        loader.run(pcd_file, points_file)
        
    except rospy.ROSInterruptException:
        pass
