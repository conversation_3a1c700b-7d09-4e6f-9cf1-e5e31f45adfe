<?xml version="1.0"?>
<package>
    <name>faster_lio</name>
    <version>0.0.0</version>

    <description>
        FasterLIO is developed from FastLIO2, see related paper: <PERSON><PERSON> et al. Faster-LIO: Lightweight Tightly
        Coupled Lidar-inertial Odometry
        using Parallel Sparse Incremental Voxels, IEEE RA-Letters, 2022
    </description>

    <maintainer email="<EMAIL>">gaoxiang</maintainer>

    <license>BSD</license>

    <author email="<EMAIL>">Gao Xiang</author>

    <buildtool_depend>catkin</buildtool_depend>
    <build_depend>geometry_msgs</build_depend>
    <build_depend>nav_msgs</build_depend>
    <build_depend>roscpp</build_depend>
    <build_depend>rospy</build_depend>
    <build_depend>std_msgs</build_depend>
    <build_depend>sensor_msgs</build_depend>
    <build_depend>tf</build_depend>
    <build_depend>pcl_ros</build_depend>
    <build_depend>message_generation</build_depend>
    <build_depend>libgoogle-glog-dev</build_depend>

    <run_depend>geometry_msgs</run_depend>
    <run_depend>nav_msgs</run_depend>
    <run_depend>sensor_msgs</run_depend>
    <run_depend>roscpp</run_depend>
    <run_depend>rospy</run_depend>
    <run_depend>std_msgs</run_depend>
    <run_depend>tf</run_depend>
    <run_depend>pcl_ros</run_depend>
    <run_depend>message_runtime</run_depend>
    <run_depend>libgoogle-glog-dev</run_depend>

    <test_depend>rostest</test_depend>
    <test_depend>rosbag</test_depend>

    <export>
    </export>
</package>
